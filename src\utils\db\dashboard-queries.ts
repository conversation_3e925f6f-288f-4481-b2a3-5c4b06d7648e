import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { format, startOfYear, subDays, subMonths, subYears } from "date-fns";
import {
  checkExchangeRateAssetsExist,
  convertAmount,
  ExchangeRateMap,
  extractPortfolioCurrencies,
  fetchExchangeRatesFromDB,
  fetchMissingExchangeRates,
  getRequiredExchangeRates,
} from "./exchange-rates";
import { hasuraQuery } from "./hasura";

// Types for dashboard data
export interface PortfolioCompositionItem {
  name: string;
  value: number;
  percentage: number;
  ticker?: string; // Only for positions view
}

export interface PortfolioComposition {
  sector: PortfolioCompositionItem[];
  industry: PortfolioCompositionItem[];
  currency: PortfolioCompositionItem[];
  country: PortfolioCompositionItem[];
  assetType: PortfolioCompositionItem[];
  positions: PortfolioCompositionItem[];
  totalValue: number;
  displayCurrency: SupportedCurrency;
}

// Types for portfolio performance data
export interface PortfolioPerformanceDataPoint {
  date: string;
  value: number;
  profitLoss: number;
  profitLossPercentage: number;
}

export interface PortfolioPerformanceData {
  data: PortfolioPerformanceDataPoint[];
  currentValue: number;
  basePortfolioValue: number; // Cost basis for current holdings
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  displayCurrency: SupportedCurrency;
  timePeriod: TimePeriod;
}

export type TimePeriod = "1W" | "1M" | "YTD" | "1Y" | "MAX";

export interface TransactionWithAssetDetails {
  id: string;
  portfolio_id: string;
  ticker: string;
  price: number;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  transaction_fee?: number;
  asset?: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    sector?: { name: string };
    industry?: { name: string };
    currency?: { code: string; name: string; symbol: string };
    country?: { code: string; name: string };
    asset_type?: { name: string };
  };
  latest_price?: {
    close_price: number;
    date: string;
  };
}

// Portfolio Metrics Types
export interface PortfolioMetrics {
  capitalInvested: number; // Total invested capital
  priceGain: number; // Unrealized capital appreciation
  priceGainPercentage: number;
  dividends: number; // Total dividend income
  dividendsPercentage: number;
  realizedGain: number; // Gains from sold positions
  realizedGainPercentage: number;
  transactionCosts: number; // Sum of transaction fees
  taxes: number; // Sum of taxes (currently same as transaction fees)
  totalReturn: number; // Total portfolio return
  twrr: number; // Time-Weighted Rate of Return
  mwrr: number; // Money-Weighted Rate of Return
  displayCurrency: SupportedCurrency;
}

export interface DividendData {
  dividend_id: number;
  asset_id: number;
  ex_date: string;
  amount_per_share: number;
  dividend_type: string;
  status: string;
  asset?: {
    ticker?: string;
    currency?: {
      code: string;
      name: string;
      symbol: string;
    };
  };
}

// Types for Dividends Card
export interface DividendYearData {
  year: number;
  totalAmount: number;
  totalAmountEUR?: number; // Optional EUR amount for currency-independent CAGR calculation
  assetBreakdown: Array<{
    ticker: string;
    company: string;
    amount: number;
    color: string;
  }>;
}

export interface DividendTableRow {
  ticker: string;
  company: string;
  status: string;
  payments: number;
  payout: number;
  yield: number;
  yoyGrowth: number;
}

export interface DividendCardData {
  totalDividends: number;
  dividendYieldTTM: number; // Placeholder for now
  yocTTM: number; // Placeholder for now
  cagrPayouts: number; // Placeholder for now
  yearlyData: DividendYearData[];
  tableData: Record<number, DividendTableRow[]>; // Year -> rows
  displayCurrency: SupportedCurrency;
}

// Types for Companies Data Table
export interface CompanyData {
  ticker: string;
  company: string;
  logo_url?: string;
  shares: number; // Total quantity across selected portfolios
  currentPrice: number; // Latest price converted to display currency
  avgCost: number; // Average purchase price from BUY transactions
  costValue: number; // Total amount spent on BUY transactions
  marketValue: number; // Current quantity × latest price
  dollarReturn: number; // Market Value - Cost Value
  percentReturn: number; // (Market Value - Cost Value) / Cost Value × 100
  allocation: number; // Percentage of this asset in total portfolio value
  yesterdayChange: number; // Price change from previous day
  dividendIncome: number; // Total dividend income received
  dividendPayments: number; // Number of dividend payments received
  // Asset details
  sector?: string;
  industry?: string;
  country?: string;
  currency: string;
  // Metrics
  fiftyTwoWeekLow?: number;
  fiftyTwoWeekHigh?: number;
  fiftyTwoWeekLowConverted?: number; // 52W Low converted to display currency
  fiftyTwoWeekHighConverted?: number; // 52W High converted to display currency
  dividendYield?: number;
  // DCA calculations
  dcaPrice?: number; // Dollar Cost Average price in display currency
  dcaPercentage?: number; // DCA percentage: ((Current Price - DCA Price) / DCA Price) × 100
  // Transaction fees
  totalTransactionFees: number; // Sum of all transaction fees in display currency
}

export interface CompanyDataWithAsset extends CompanyData {
  asset: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    logo_url?: string;
    sector?: { name: string };
    industry?: { name: string };
    currency?: { code: string; name: string; symbol: string };
    country?: { code: string; name: string };
    asset_type?: { name: string };
  };
  latestPrice?: {
    close_price: number;
    date: string;
  };
  previousPrice?: {
    close_price: number;
    date: string;
  };
  metrics?: {
    fifty_two_week_low?: number;
    fifty_two_week_high?: number;
    dividend_yield?: number;
  };
  transactions: TransactionWithAssetDetails[];
  dividends: DividendData[];
}

// GraphQL Queries

/**
 * Get portfolio composition data with asset details and reference tables
 * This query fetches transactions with complete asset information
 */
export const GET_PORTFOLIO_COMPOSITION_DATA = `
  query GetPortfolioCompositionData($portfolioIds: [uuid!]!) {
    ptvuser_transactions(
      where: {portfolio_id: {_in: $portfolioIds}}
      order_by: {transaction_date: desc}
    ) {
      id
      portfolio_id
      ticker
      price
      quantity
      transaction_date
      transaction_type
      transaction_fee
    }
  }
`;

/**
 * Get asset details with all reference table joins
 */
export const GET_ASSETS_WITH_REFERENCES = `
  query GetAssetsWithReferences($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
      company
      sector {
        name
      }
      industry {
        name
      }
      currency {
        code
        name
        symbol
      }
      country {
        code
        name
      }
      asset_type {
        name
      }
    }
  }
`;

/**
 * Get latest asset prices for portfolio tickers
 */
export const GET_LATEST_ASSET_PRICES = `
  query GetLatestAssetPrices($tickers: [String!]!, $today: date!) {
    ptvuser_asset(where: { ticker: { _in: $tickers } }) {
      ticker
      asset_id
      latest_price: asset_prices(
        where: { date: { _lt: $today } }
        order_by: { date: desc }
        limit: 1
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get comprehensive company data for the companies table
 * This query fetches all required data for the companies dashboard card
 */
export const GET_COMPANIES_DATA = `
  query GetCompaniesData($portfolioIds: [uuid!]!) {
    ptvuser_transactions(
      where: {portfolio_id: {_in: $portfolioIds}}
      order_by: {transaction_date: desc}
    ) {
      id
      portfolio_id
      ticker
      price
      quantity
      transaction_date
      transaction_type
      transaction_fee
    }
  }
`;

/**
 * Get asset details with metrics for companies table
 */
export const GET_ASSETS_WITH_METRICS = `
  query GetAssetsWithMetrics($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
      company
      logo_url
      sector {
        name
      }
      industry {
        name
      }
      currency {
        code
        name
        symbol
      }
      country {
        code
        name
      }
      asset_type {
        name
      }
      latest_metrics: asset_metrics(
        order_by: {date: desc}
        limit: 1
      ) {
        fifty_two_week_low
        fifty_two_week_high
        dividend_yield
        date
      }
    }
  }
`;

/**
 * Get latest and previous day prices for companies table
 */
export const GET_COMPANIES_PRICES = `
  query GetCompaniesPrices($tickers: [String!]!, $today: date!) {
    ptvuser_asset(where: { ticker: { _in: $tickers } }) {
      ticker
      asset_id
      latest_price: asset_prices(
        where: { date: { _lt: $today } }
        order_by: { date: desc }
        limit: 1
      ) {
        close_price: adj_close
        date
      }
      previous_price: asset_prices(
        where: { date: { _lt: $today } }
        order_by: { date: desc }
        limit: 2
        offset: 1
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get dividend data for companies table
 */
export const GET_COMPANIES_DIVIDENDS = `
  query GetCompaniesDividends($assetIds: [Int!]!) {
    ptvuser_dividend(
      where: {
        asset_id: {_in: $assetIds}
        status: {_eq: "Paid"}
      }
      order_by: {ex_date: desc}
    ) {
      dividend_id
      asset_id
      ex_date
      amount_per_share
      dividend_type
      status
      asset {
        ticker
        currency {
          code
          name
          symbol
        }
      }
    }
  }
`;

/**
 * Get historical asset prices for specific date range
 */
export const GET_HISTORICAL_ASSET_PRICES = `
  query GetHistoricalAssetPrices($tickers: [String!]!, $startDate: date!, $endDate: date!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      asset_id
      historical_prices: asset_prices(
        where: {date: {_gte: $startDate, _lte: $endDate}}
        order_by: {date: asc}
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get asset prices for specific dates (for monthly/YTD calculations)
 * @deprecated This query is no longer used as all time periods now use daily data
 */
export const GET_ASSET_PRICES_FOR_DATES = `
  query GetAssetPricesForDates($tickers: [String!]!, $dates: [date!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      asset_id
      price_data: asset_prices(
        where: {date: {_in: $dates}}
        order_by: {date: asc}
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get dividends for portfolio assets
 */
export const GET_PORTFOLIO_DIVIDENDS = `
  query GetPortfolioDividends($assetIds: [Int!]!) {
    ptvuser_dividend(
      where: {asset_id: {_in: $assetIds}}
      order_by: {ex_date: desc}
    ) {
      dividend_id
      asset_id
      ex_date
      amount_per_share
      dividend_type
      status
      asset {
        ticker
        currency {
          code
          name
          symbol
        }
      }
    }
  }
`;

/**
 * Get comprehensive dividend data for dividends card
 */
export const GET_DIVIDENDS_CARD_DATA = `
  query GetDividendsCardData($assetIds: [Int!]!) {
    ptvuser_dividend(
      where: {asset_id: {_in: $assetIds}}
      order_by: {ex_date: desc}
    ) {
      dividend_id
      asset_id
      ex_date
      amount_per_share
      dividend_type
      status
      asset {
        ticker
        name
        company
        currency {
          code
          name
          symbol
        }
      }
    }
  }
`;

// Utility Functions

/**
 * Find the earliest transaction date from a list of transactions
 * Prioritizes the first BUY transaction, ignoring SELL transactions that occur before any BUY
 */
export function findEarliestTransactionDate(
  transactions: TransactionWithAssetDetails[]
): string | null {
  if (transactions.length === 0) {
    return null;
  }

  // Sort transactions by date
  const sortedTransactions = transactions
    .slice()
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  // Find the first BUY transaction
  const firstBuyTransaction = sortedTransactions.find(
    (t) => t.transaction_type === "BUY"
  );

  if (firstBuyTransaction) {
    return firstBuyTransaction.transaction_date;
  }

  // If no BUY transactions found, return the earliest transaction date
  return sortedTransactions[0].transaction_date;
}

/**
 * Calculate date range for different time periods
 */
export function calculateDateRange(
  timePeriod: TimePeriod,
  transactions?: TransactionWithAssetDetails[]
): {
  startDate: string;
  endDate: string;
} {
  const now = new Date();
  let startDate: string;
  let endDate: string;

  switch (timePeriod) {
    case "1W":
      endDate = format(subDays(now, 1), "yyyy-MM-dd"); // yesterday
      startDate = format(subDays(now, 7 + 1), "yyyy-MM-dd"); // 1 week before yesterday
      break;
    case "1M":
      endDate = format(subDays(now, 1), "yyyy-MM-dd"); // yesterday
      startDate = format(subMonths(now, 1), "yyyy-MM-dd");
      break;
    case "YTD":
      endDate = format(now, "yyyy-MM-dd"); // today
      startDate = format(startOfYear(now), "yyyy-MM-dd");
      break;
    case "1Y":
      endDate = format(now, "yyyy-MM-dd"); // today
      startDate = format(subYears(now, 1), "yyyy-MM-dd");
      break;
    case "MAX":
      endDate = format(now, "yyyy-MM-dd"); // today
      if (!transactions || transactions.length === 0) {
        startDate = format(subYears(now, 1), "yyyy-MM-dd");
      } else {
        const earliestDate = findEarliestTransactionDate(transactions);
        console.log("earliest", earliestDate);
        startDate = earliestDate
          ? format(subDays(earliestDate, 1), "yyyy-MM-dd")
          : endDate;
      }
      console.log(startDate, endDate);
      break;
    default:
      throw new Error(`Unsupported time period: ${timePeriod}`);
  }

  return { startDate, endDate };
}

/**
 * Generate date array for monthly sampling (last day of each month)
 * @deprecated This function is no longer used as all time periods now use daily data
 */
export function generateMonthlyDates(
  startDate: string,
  endDate: string
): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const current = new Date(start.getFullYear(), start.getMonth(), 1);

  while (current <= end) {
    // Get last day of current month
    const lastDay = new Date(current.getFullYear(), current.getMonth() + 1, 0);
    if (lastDay <= end) {
      dates.push(lastDay.toISOString().split("T")[0]);
    }
    // Move to next month
    current.setMonth(current.getMonth() + 1);
  }

  // Always include the end date if it's not already included
  const endDateStr = end.toISOString().split("T")[0];
  if (!dates.includes(endDateStr)) {
    dates.push(endDateStr);
  }

  return dates;
}

/**
 * Determine if YTD should use daily or monthly frequency
 * @deprecated This function is no longer used as all time periods now use daily data
 */
export function shouldUseMonthlyForYTD(): boolean {
  const now = new Date();
  const yearStart = new Date(now.getFullYear(), 0, 1);
  const monthsDiff =
    (now.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24 * 30.44); // Average month length
  return monthsDiff > 3;
}

/**
 * Calculate portfolio holdings from transactions
 */
export function calculatePortfolioHoldings(
  transactions: TransactionWithAssetDetails[]
): Map<
  string,
  { quantity: number; transactions: TransactionWithAssetDetails[] }
> {
  const holdings = new Map<
    string,
    { quantity: number; transactions: TransactionWithAssetDetails[] }
  >();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, transactions: [] };

    const quantity =
      transaction.transaction_type === "BUY"
        ? transaction.quantity
        : -transaction.quantity;

    holdings.set(ticker, {
      quantity: existing.quantity + quantity,
      transactions: [...existing.transactions, transaction],
    });
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((value, key) => {
    if (value.quantity > 0) {
      filteredHoldings.set(key, value);
    }
  });

  return filteredHoldings;
}

/**
 * Calculate dividend income for a portfolio based on holdings and ex-dates
 */
export function calculateDividendIncome(
  transactions: TransactionWithAssetDetails[],
  dividends: DividendData[],
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): number {
  let totalDividendIncome = 0;

  // Group dividends by asset_id
  const dividendsByAsset = new Map<number, DividendData[]>();
  dividends.forEach((dividend) => {
    const existing = dividendsByAsset.get(dividend.asset_id) || [];
    dividendsByAsset.set(dividend.asset_id, [...existing, dividend]);
  });

  // For each asset with dividends, calculate income based on holdings at ex-date
  dividendsByAsset.forEach((assetDividends, assetId) => {
    // Find the ticker for this asset_id
    const asset = Array.from(assetData.values()).find(
      (a) => a?.asset_id === assetId
    );
    if (!asset) return;

    const ticker = asset.ticker;
    const assetCurrency = asset.currency?.code || "EUR";

    assetDividends.forEach((dividend) => {
      // Calculate holdings at the ex-date
      const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
        transactions.filter((t) => t.ticker === ticker),
        dividend.ex_date
      );

      const holding = holdingsAtExDate.get(ticker);
      if (holding && holding.quantity > 0) {
        // Get currency from dividend's asset relationship or fallback to asset data
        const dividendCurrency =
          dividend.asset?.currency?.code || assetCurrency;

        // Calculate dividend income in original currency
        const dividendIncomeOriginal =
          holding.quantity * dividend.amount_per_share;

        // Convert to display currency
        const convertedDividendIncome = convertAmount(
          dividendIncomeOriginal,
          dividendCurrency,
          displayCurrency,
          exchangeRates
        );

        totalDividendIncome += convertedDividendIncome;
      }
    });
  });

  return totalDividendIncome;
}

/**
 * Calculate realized gains from completed buy/sell pairs
 */
export function calculateRealizedGains(
  transactions: TransactionWithAssetDetails[],
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): number {
  let totalRealizedGains = 0;

  // Group transactions by ticker
  const transactionsByTicker = new Map<string, TransactionWithAssetDetails[]>();
  transactions.forEach((transaction) => {
    const existing = transactionsByTicker.get(transaction.ticker) || [];
    transactionsByTicker.set(transaction.ticker, [...existing, transaction]);
  });

  // For each ticker, calculate realized gains using FIFO method
  transactionsByTicker.forEach((tickerTransactions, ticker) => {
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Sort transactions by date
    const sortedTransactions = tickerTransactions.sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

    const buyQueue: Array<{ quantity: number; price: number; date: string }> =
      [];
    let realizedGainForTicker = 0;

    sortedTransactions.forEach((transaction) => {
      if (transaction.transaction_type === "BUY") {
        buyQueue.push({
          quantity: transaction.quantity,
          price: transaction.price,
          date: transaction.transaction_date,
        });
      } else if (transaction.transaction_type === "SELL") {
        let remainingSellQuantity = transaction.quantity;
        const sellPrice = transaction.price;

        while (remainingSellQuantity > 0 && buyQueue.length > 0) {
          const oldestBuy = buyQueue[0];
          const quantityToSell = Math.min(
            remainingSellQuantity,
            oldestBuy.quantity
          );

          // Calculate realized gain for this portion
          const gainPerShare = sellPrice - oldestBuy.price;
          const realizedGainOriginal = gainPerShare * quantityToSell;

          // Debug logging for TLV.RO
          if (ticker === "TLV.RO") {
            console.log(`🔍 TLV.RO Realized Gain Calculation:`, {
              sellPrice,
              buyPrice: oldestBuy.price,
              gainPerShare,
              quantityToSell,
              realizedGainOriginal,
              assetCurrency,
              displayCurrency,
            });
          }

          // Convert to display currency
          const convertedRealizedGain = convertAmount(
            realizedGainOriginal,
            assetCurrency,
            displayCurrency,
            exchangeRates
          );

          if (ticker === "TLV.RO") {
            console.log(`💱 TLV.RO Currency Conversion:`, {
              originalAmount: realizedGainOriginal,
              convertedAmount: convertedRealizedGain,
              exchangeRates: Array.from(exchangeRates.entries()),
            });
          }

          realizedGainForTicker += convertedRealizedGain;

          // Update quantities
          remainingSellQuantity -= quantityToSell;
          oldestBuy.quantity -= quantityToSell;

          // Remove buy order if fully consumed
          if (oldestBuy.quantity === 0) {
            buyQueue.shift();
          }
        }
      }
    });

    // Debug logging for TLV.RO total
    if (ticker === "TLV.RO") {
      console.log(
        `📊 TLV.RO Total Realized Gain: ${realizedGainForTicker} ${displayCurrency}`
      );
    }

    totalRealizedGains += realizedGainForTicker;
  });

  return totalRealizedGains;
}

/**
 * Calculate total transaction costs (fees)
 */
export function calculateTransactionCosts(
  transactions: TransactionWithAssetDetails[],
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): number {
  let totalCosts = 0;

  transactions.forEach((transaction) => {
    if (transaction.transaction_fee && transaction.transaction_fee > 0) {
      // Get the asset data to determine the currency
      const asset = assetData.get(transaction.ticker);
      const assetCurrency = asset?.currency?.code || "EUR";

      // Convert transaction fee from asset currency to display currency
      const convertedFee = convertAmount(
        transaction.transaction_fee,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );

      totalCosts += convertedFee;
    }
  });

  return totalCosts;
}

/**
 * Calculate comprehensive portfolio metrics
 */
export function calculatePortfolioMetrics(
  transactions: TransactionWithAssetDetails[],
  latestPrices: Map<string, number>,
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  dividends: DividendData[],
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioMetrics {
  const currentHoldings = calculatePortfolioHoldings(transactions);

  // 1. Capital Investit (Total Invested Capital)
  let capitalInvested = 0;
  currentHoldings.forEach((holding, ticker) => {
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate total cost basis for current holdings
    let totalCostBasis = 0;
    let totalBuyQuantity = 0;

    holding.transactions.forEach((t) => {
      if (t.transaction_type === "BUY") {
        totalCostBasis += t.price * t.quantity;
        totalBuyQuantity += t.quantity;
      }
    });

    const avgCostPerShare =
      totalBuyQuantity > 0 ? totalCostBasis / totalBuyQuantity : 0;
    const costBasisInOriginalCurrency = holding.quantity * avgCostPerShare;

    const convertedCostBasis = convertAmount(
      costBasisInOriginalCurrency,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    capitalInvested += convertedCostBasis;
  });

  // 2. Current Portfolio Value
  let currentValue = 0;
  currentHoldings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    const originalValue = holding.quantity * latestPrice;
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    currentValue += convertedValue;
  });

  // 3. Price Gain (Unrealized Capital Appreciation)
  const priceGain = currentValue - capitalInvested;
  const priceGainPercentage =
    capitalInvested > 0 ? (priceGain / capitalInvested) * 100 : 0;

  // 4. Dividends (Total Dividend Income)
  const dividendsIncome = calculateDividendIncome(
    transactions,
    dividends,
    assetData,
    displayCurrency,
    exchangeRates
  );
  const dividendsPercentage =
    capitalInvested > 0 ? (dividendsIncome / capitalInvested) * 100 : 0;

  // 5. Realized Gain (Gains from Sold Positions)
  const realizedGain = calculateRealizedGains(
    transactions,
    assetData,
    displayCurrency,
    exchangeRates
  );
  const realizedGainPercentage =
    capitalInvested > 0 ? (realizedGain / capitalInvested) * 100 : 0;

  // 6. Transaction Costs
  const transactionCosts = calculateTransactionCosts(
    transactions,
    assetData,
    displayCurrency,
    exchangeRates
  );

  // 7. Taxes (currently same as transaction costs)
  const taxes = transactionCosts;

  // 8. Total Return
  const totalReturn = priceGain + dividendsIncome + realizedGain;

  // 9. TWRR (Time-Weighted Rate of Return) - simplified calculation
  // const twrr = capitalInvested > 0 ? (totalReturn / capitalInvested) * 100 : 0;
  const twrr = 0;

  // 10. MWRR (Money-Weighted Rate of Return) - simplified calculation
  // For now, using the same as TWRR. A more sophisticated calculation would consider cash flow timing
  const mwrr = 0;

  return {
    capitalInvested,
    priceGain,
    priceGainPercentage,
    dividends: dividendsIncome,
    dividendsPercentage,
    realizedGain,
    realizedGainPercentage,
    transactionCosts,
    taxes,
    totalReturn,
    twrr,
    mwrr,
    displayCurrency,
  };
}

/**
 * Calculate portfolio composition by different categories
 */
export function calculatePortfolioComposition(
  transactions: TransactionWithAssetDetails[],
  latestPrices: Map<string, number>,
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioComposition {
  const holdings = calculatePortfolioHoldings(transactions);

  // Calculate current values for each holding
  const holdingValues = new Map<string, number>();
  let totalValue = 0;

  holdings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR"; // Default to EUR if no currency info

    // Calculate value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    holdingValues.set(ticker, convertedValue);
    totalValue += convertedValue;
  });

  // Group by different categories
  const sectorMap = new Map<string, number>();
  const industryMap = new Map<string, number>();
  const currencyMap = new Map<string, number>();
  const countryMap = new Map<string, number>();
  const assetTypeMap = new Map<string, number>();
  const positionsArray: PortfolioCompositionItem[] = [];

  holdings.forEach((_, ticker) => {
    const value = holdingValues.get(ticker) || 0;
    const asset = assetData.get(ticker);

    if (value === 0) return;

    // Positions
    positionsArray.push({
      name: `${asset?.name} (${ticker})`,
      ticker,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
    });

    // Sector
    const sectorName = asset?.sector?.name || "Altele / Diversificat";
    sectorMap.set(sectorName, (sectorMap.get(sectorName) || 0) + value);

    // Industry
    const industryName = asset?.industry?.name || "Altele / Diversificat";
    industryMap.set(industryName, (industryMap.get(industryName) || 0) + value);

    // Currency
    const currencyName =
      asset?.currency?.name || asset?.currency?.code || "Altele";
    currencyMap.set(currencyName, (currencyMap.get(currencyName) || 0) + value);

    // Country
    const countryName = asset?.country?.name || "Diversificat";
    if (countryName === "USA" || countryName === "United States") {
      countryMap.set(
        "United States",
        (countryMap.get("United States") || 0) + value
      );
    } else {
      countryMap.set(countryName, (countryMap.get(countryName) || 0) + value);
    }

    // Asset Type
    let assetTypeName = asset?.asset_type?.name || "Altele";
    // Convert EQUITY to STOCK as requested
    if (assetTypeName === "EQUITY" || assetTypeName === "Common Stock") {
      assetTypeName = "STOCK";
    }
    assetTypeMap.set(
      assetTypeName,
      (assetTypeMap.get(assetTypeName) || 0) + value
    );
  });

  // Convert maps to arrays with percentages
  const createCompositionArray = (
    map: Map<string, number>
  ): PortfolioCompositionItem[] => {
    return Array.from(map.entries())
      .map(([name, value]) => ({
        name,
        value,
        percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
      }))
      .sort((a, b) => b.value - a.value);
  };

  return {
    sector: createCompositionArray(sectorMap),
    industry: createCompositionArray(industryMap),
    currency: createCompositionArray(currencyMap),
    country: createCompositionArray(countryMap),
    assetType: createCompositionArray(assetTypeMap),
    positions: positionsArray.sort((a, b) => b.value - a.value),
    totalValue,
    displayCurrency,
  };
}

/**
 * Calculate company holdings from transactions
 */
export function calculateCompanyHoldings(
  transactions: TransactionWithAssetDetails[]
): Map<
  string,
  {
    shares: number;
    buyTransactions: TransactionWithAssetDetails[];
    allTransactions: TransactionWithAssetDetails[];
  }
> {
  const holdings = new Map<
    string,
    {
      shares: number;
      buyTransactions: TransactionWithAssetDetails[];
      allTransactions: TransactionWithAssetDetails[];
    }
  >();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || {
      shares: 0,
      buyTransactions: [],
      allTransactions: [],
    };

    // Track all transactions for this ticker
    existing.allTransactions.push(transaction);

    // Calculate net shares
    if (transaction.transaction_type === "BUY") {
      existing.shares += transaction.quantity;
      existing.buyTransactions.push(transaction);
    } else if (transaction.transaction_type === "SELL") {
      existing.shares -= transaction.quantity;
    }

    holdings.set(ticker, existing);
  });

  // Filter out holdings with zero or negative shares
  const filteredHoldings = new Map<
    string,
    {
      shares: number;
      buyTransactions: TransactionWithAssetDetails[];
      allTransactions: TransactionWithAssetDetails[];
    }
  >();
  holdings.forEach((holding, ticker) => {
    if (holding.shares > 0) {
      filteredHoldings.set(ticker, holding);
    }
  });

  return filteredHoldings;
}

/**
 * Calculate average cost for a ticker from BUY transactions
 */
export function calculateAverageCost(
  buyTransactions: TransactionWithAssetDetails[]
): number {
  if (buyTransactions.length === 0) return 0;

  let totalCost = 0;
  let totalShares = 0;

  buyTransactions.forEach((transaction) => {
    totalCost += transaction.price * transaction.quantity;
    totalShares += transaction.quantity;
  });

  return totalShares > 0 ? totalCost / totalShares : 0;
}

/**
 * Calculate total cost value for a ticker from BUY transactions
 */
export function calculateCostValue(
  buyTransactions: TransactionWithAssetDetails[]
): number {
  return buyTransactions.reduce((total, transaction) => {
    return total + transaction.price * transaction.quantity;
  }, 0);
}

/**
 * Calculate Dollar Cost Average (DCA) price for a ticker from BUY transactions
 * Formula: DCA Price = Σ(Qi × Pi) / ΣQi
 * Where Qi = quantity of shares in transaction i, Pi = price per share in transaction i
 */
export function calculateDCAPrice(
  buyTransactions: TransactionWithAssetDetails[]
): number {
  if (buyTransactions.length === 0) return 0;

  let totalWeightedCost = 0; // Σ(Qi × Pi)
  let totalQuantity = 0; // ΣQi

  buyTransactions.forEach((transaction) => {
    totalWeightedCost += transaction.quantity * transaction.price;
    totalQuantity += transaction.quantity;
  });

  return totalQuantity > 0 ? totalWeightedCost / totalQuantity : 0;
}

/**
 * Calculate DCA percentage for a ticker
 * Formula: %DCA = ((Current Price - DCA Price) / DCA Price) × 100
 */
export function calculateDCAPercentage(
  currentPrice: number,
  dcaPrice: number
): number {
  if (dcaPrice <= 0) return 0;
  return ((currentPrice - dcaPrice) / dcaPrice) * 100;
}

/**
 * Calculate total transaction fees for a ticker from all transactions
 * Converts fees from asset currency to display currency
 */
export function calculateTickerTransactionFees(
  transactions: TransactionWithAssetDetails[],
  assetCurrency: string,
  displayCurrency: SupportedCurrency,
  exchangeRates: ExchangeRateMap
): number {
  let totalFees = 0;

  transactions.forEach((transaction) => {
    if (transaction.transaction_fee && transaction.transaction_fee > 0) {
      // Convert transaction fee from asset currency to display currency
      const convertedFee = convertAmount(
        transaction.transaction_fee,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );
      totalFees += convertedFee;
    }
  });

  return totalFees;
}

/**
 * Calculate dividend income for a specific ticker based on holdings at ex-dates
 */
export function calculateTickerDividendIncome(
  ticker: string,
  transactions: TransactionWithAssetDetails[],
  dividends: DividendData[],
  assetCurrency: string,
  displayCurrency: SupportedCurrency,
  exchangeRates: ExchangeRateMap
): number {
  let totalDividendIncome = 0;

  // Filter transactions for this specific ticker
  const tickerTransactions = transactions.filter((t) => t.ticker === ticker);

  dividends.forEach((dividend) => {
    // Calculate holdings at the ex-date
    const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
      tickerTransactions,
      dividend.ex_date
    );

    const holding = holdingsAtExDate.get(ticker);
    if (holding && holding.quantity > 0) {
      // Get currency from dividend's asset relationship or fallback to asset data
      const dividendCurrency = dividend.asset?.currency?.code || assetCurrency;

      // Calculate dividend income in original currency
      const dividendIncomeOriginal =
        holding.quantity * dividend.amount_per_share;

      // Convert to display currency
      const convertedDividendIncome = convertAmount(
        dividendIncomeOriginal,
        dividendCurrency,
        displayCurrency,
        exchangeRates
      );

      totalDividendIncome += convertedDividendIncome;
    }
  });

  return totalDividendIncome;
}

/**
 * Calculate dividend payment count for a specific ticker based on holdings at ex-dates
 */
export function calculateTickerDividendPayments(
  ticker: string,
  transactions: TransactionWithAssetDetails[],
  dividends: DividendData[]
): number {
  let totalPayments = 0;

  // Filter transactions for this specific ticker
  const tickerTransactions = transactions.filter((t) => t.ticker === ticker);

  dividends.forEach((dividend) => {
    // Calculate holdings at the ex-date
    const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
      tickerTransactions,
      dividend.ex_date
    );

    const holding = holdingsAtExDate.get(ticker);
    if (holding && holding.quantity > 0) {
      // If we had holdings at the ex-date, count this as a payment
      totalPayments += 1;
    }
  });

  return totalPayments;
}

/**
 * Calculate dividend data for the dividends card
 */
export function calculateDividendCardData(
  transactions: TransactionWithAssetDetails[],
  dividends: DividendData[],
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map(),
  latestPrices: Map<string, number> = new Map()
): DividendCardData {
  // Calculate total dividends (same as in metrics card)
  const totalDividends = calculateDividendIncome(
    transactions,
    dividends,
    assetData,
    displayCurrency,
    exchangeRates
  );

  // Generate last 6 years of data
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 6 }, (_, i) => currentYear - i).reverse();

  const yearlyData: DividendYearData[] = [];
  const tableData: Record<number, DividendTableRow[]> = {};

  // Color palette for different assets
  const colors = [
    "#3b82f6",
    "#ef4444",
    "#10b981",
    "#f59e0b",
    "#8b5cf6",
    "#06b6d4",
    "#84cc16",
    "#f97316",
    "#ec4899",
    "#6366f1",
  ];

  years.forEach((year) => {
    const yearStart = `${year}-01-01`;
    const yearEnd = `${year}-12-31`;

    // Filter dividends for this year
    const yearDividends = dividends.filter(
      (dividend) =>
        dividend.ex_date >= yearStart &&
        dividend.ex_date <= yearEnd &&
        dividend.status === "Paid"
    );

    // Calculate dividend amounts by asset for this year
    const assetDividends = new Map<
      string,
      { amount: number; amountEUR: number; company: string; payments: number }
    >();

    yearDividends.forEach((dividend) => {
      const asset = Array.from(assetData.values()).find(
        (a) => a?.asset_id === dividend.asset_id
      );
      if (!asset) return;

      const ticker = asset.ticker;
      const assetCurrency = asset.currency?.code || "EUR";

      // Calculate holdings at the ex-date
      const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
        transactions.filter((t) => t.ticker === ticker),
        dividend.ex_date
      );

      const holding = holdingsAtExDate.get(ticker);
      if (holding && holding.quantity > 0) {
        const dividendCurrency =
          dividend.asset?.currency?.code || assetCurrency;
        const dividendIncomeOriginal =
          holding.quantity * dividend.amount_per_share;

        // For display purposes, convert to display currency
        const convertedDividendIncome = convertAmount(
          dividendIncomeOriginal,
          dividendCurrency,
          displayCurrency,
          exchangeRates
        );

        // For CAGR calculation, also store in EUR for currency-independent calculation
        const dividendIncomeEUR = convertAmount(
          dividendIncomeOriginal,
          dividendCurrency,
          "EUR",
          exchangeRates
        );

        const existing = assetDividends.get(ticker) || {
          amount: 0,
          amountEUR: 0, // Add EUR amount for CAGR calculation
          company: asset.company || asset.name,
          payments: 0,
        };
        assetDividends.set(ticker, {
          amount: existing.amount + convertedDividendIncome,
          amountEUR: existing.amountEUR + dividendIncomeEUR,
          company: existing.company,
          payments: existing.payments + 1,
        });
      }
    });

    // Create yearly data
    let totalYearAmount = 0;
    let totalYearAmountEUR = 0; // For CAGR calculation
    const assetBreakdown: DividendYearData["assetBreakdown"] = [];
    let colorIndex = 0;

    assetDividends.forEach((data, ticker) => {
      totalYearAmount += data.amount;
      totalYearAmountEUR += data.amountEUR;
      assetBreakdown.push({
        ticker,
        company: data.company,
        amount: data.amount,
        color: colors[colorIndex % colors.length],
      });
      colorIndex++;
    });

    yearlyData.push({
      year,
      totalAmount: totalYearAmount,
      totalAmountEUR: totalYearAmountEUR, // Add EUR amount for CAGR
      assetBreakdown: assetBreakdown.sort((a, b) => b.amount - a.amount),
    });

    // Create table data for this year
    const tableRows: DividendTableRow[] = [];
    assetDividends.forEach((data, ticker) => {
      tableRows.push({
        ticker,
        company: data.company,
        status: "Paid", // For now, all are paid since we filter by status
        payments: data.payments,
        payout: data.amount,
        yield: 0, // Placeholder - would need current price and holdings
        yoyGrowth: 0, // Placeholder - would need historical data
      });
    });

    tableData[year] = tableRows.sort((a, b) => b.payout - a.payout);
  });

  // Calculate Dividend Yield (TTM) - Trailing Twelve Months
  // Currency-independent calculation using EUR as base currency
  const calculateDividendYieldTTM = (): number => {
    // Get date 12 months ago from today
    const today = new Date();
    const twelveMonthsAgo = new Date(today);
    twelveMonthsAgo.setFullYear(today.getFullYear() - 1);

    const ttmStartDate = twelveMonthsAgo.toISOString().split("T")[0];
    const ttmEndDate = today.toISOString().split("T")[0];

    // Filter dividends for the last 12 months
    const ttmDividends = dividends.filter(
      (dividend) =>
        dividend.ex_date >= ttmStartDate &&
        dividend.ex_date <= ttmEndDate &&
        dividend.status === "Paid"
    );

    if (ttmDividends.length === 0) {
      return 0;
    }

    // Calculate TTM dividend income in EUR (base currency for consistency)
    let ttmDividendIncomeEUR = 0;
    const dividendPayingTickers = new Set<string>();

    ttmDividends.forEach((dividend) => {
      const asset = Array.from(assetData.values()).find(
        (a) => a?.asset_id === dividend.asset_id
      );
      if (!asset) return;

      const ticker = asset.ticker;
      dividendPayingTickers.add(ticker);
      const assetCurrency = asset.currency?.code || "EUR";

      // Calculate holdings at the ex-date
      const holdingsAtExDate = calculatePortfolioHoldingsAtDate(
        transactions.filter((t) => t.ticker === ticker),
        dividend.ex_date
      );

      const holding = holdingsAtExDate.get(ticker);
      if (holding && holding.quantity > 0) {
        const dividendCurrency =
          dividend.asset?.currency?.code || assetCurrency;
        const dividendIncomeOriginal =
          holding.quantity * dividend.amount_per_share;

        // Convert to EUR for currency-independent calculation
        const dividendIncomeEUR = convertAmount(
          dividendIncomeOriginal,
          dividendCurrency,
          "EUR",
          exchangeRates
        );

        ttmDividendIncomeEUR += dividendIncomeEUR;
      }
    });

    // Calculate current portfolio value for dividend-paying assets only in EUR
    let currentPortfolioValueEUR = 0;
    const currentHoldings = calculatePortfolioHoldings(transactions);

    currentHoldings.forEach((holding, ticker) => {
      // Only include assets that paid dividends in TTM
      if (!dividendPayingTickers.has(ticker)) return;

      const latestPrice = latestPrices.get(ticker) || 0;
      const asset = assetData.get(ticker);
      const assetCurrency = asset?.currency?.code || "EUR";

      if (latestPrice > 0) {
        // Calculate current market value in original currency
        const originalValue = holding.quantity * latestPrice;

        // Convert to EUR for currency-independent calculation
        const valueEUR = convertAmount(
          originalValue,
          assetCurrency,
          "EUR",
          exchangeRates
        );

        currentPortfolioValueEUR += valueEUR;
      }
    });

    // Calculate dividend yield: (TTM Dividends / Current Portfolio Value) × 100
    // Both values are in EUR, so the ratio is currency-independent
    if (currentPortfolioValueEUR <= 0 || ttmDividendIncomeEUR <= 0) {
      return 0;
    }

    return (ttmDividendIncomeEUR / currentPortfolioValueEUR) * 100;
  };

  // Calculate CAGR (Compound Annual Growth Rate) for dividend payouts
  // Currency-independent calculation using EUR amounts
  const calculateDividendCAGR = (yearlyData: DividendYearData[]): number => {
    // Filter out years with zero dividends and sort by year
    // Use EUR amounts for currency-independent calculation
    const validYears = yearlyData
      .filter((year) => (year.totalAmountEUR || year.totalAmount) > 0)
      .sort((a, b) => a.year - b.year);

    // Need at least 2 years of data to calculate CAGR
    if (validYears.length < 2) {
      return 0;
    }

    const firstYear = validYears[0];
    const lastYear = validYears[validYears.length - 1];

    // Beginning and ending dividend amounts in EUR for currency independence
    const beginningDividend = firstYear.totalAmountEUR || firstYear.totalAmount;
    const endingDividend = lastYear.totalAmountEUR || lastYear.totalAmount;

    // Number of years between first and last dividend
    const numberOfYears = lastYear.year - firstYear.year;

    // Handle edge cases
    if (beginningDividend <= 0 || endingDividend <= 0 || numberOfYears <= 0) {
      return 0;
    }

    // CAGR formula: (Ending Value / Beginning Value)^(1/Number of Years) - 1
    const cagr =
      Math.pow(endingDividend / beginningDividend, 1 / numberOfYears) - 1;

    // Return as percentage (multiply by 100)
    return cagr * 100;
  };

  const dividendYieldTTM = calculateDividendYieldTTM();
  const cagrPayouts = calculateDividendCAGR(yearlyData);

  return {
    totalDividends,
    dividendYieldTTM,
    yocTTM: 1.193, // Placeholder
    cagrPayouts,
    yearlyData,
    tableData,
    displayCurrency,
  };
}

/**
 * Fetch companies data for selected portfolios
 */
export async function getCompaniesData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<CompanyData[]> {
  try {
    if (portfolioIds.length === 0) {
      return [];
    }

    // Fetch transactions
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_COMPANIES_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];
    if (transactions.length === 0) {
      return [];
    }

    // Get unique tickers
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with metrics
    const assetsResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
        company: string;
        logo_url?: string;
        sector?: { name: string };
        industry?: { name: string };
        currency?: { code: string; name: string; symbol: string };
        country?: { code: string; name: string };
        asset_type?: { name: string };
        latest_metrics?: Array<{
          fifty_two_week_low?: number;
          fifty_two_week_high?: number;
          dividend_yield?: number;
          date: string;
        }>;
      }>;
    }>(GET_ASSETS_WITH_METRICS, { variables: { tickers: uniqueTickers } });

    const assetData = new Map<string, (typeof assetsResult.ptvuser_asset)[0]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    // Fetch prices (latest and previous)
    const today = new Date().toISOString().split("T")[0];
    const pricesResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        asset_id: number;
        latest_price?: Array<{
          close_price: number;
          date: string;
        }>;
        previous_price?: Array<{
          close_price: number;
          date: string;
        }>;
      }>;
    }>(GET_COMPANIES_PRICES, { variables: { tickers: uniqueTickers, today } });

    const pricesData = new Map<
      string,
      (typeof pricesResult.ptvuser_asset)[0]
    >();
    pricesResult.ptvuser_asset?.forEach((asset) => {
      pricesData.set(asset.ticker, asset);
    });

    // Fetch dividends
    const assetIds = Array.from(assetData.values()).map(
      (asset) => asset.asset_id
    );
    const dividendsResult = await hasuraQuery<{
      ptvuser_dividend: DividendData[];
    }>(GET_COMPANIES_DIVIDENDS, { variables: { assetIds } });

    const dividendsData = new Map<number, DividendData[]>();
    dividendsResult.ptvuser_dividend?.forEach((dividend) => {
      const existing = dividendsData.get(dividend.asset_id) || [];
      existing.push(dividend);
      dividendsData.set(dividend.asset_id, existing);
    });

    // Calculate holdings
    const holdings = calculateCompanyHoldings(transactions);

    // Get exchange rates for currency conversion
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();
    if (requiredRates.length > 0) {
      try {
        // Check which exchange rate assets exist
        const existingAssets = await checkExchangeRateAssetsExist(
          requiredRates
        );
        const missingAssets = requiredRates.filter(
          (pair) => !existingAssets.has(pair)
        );

        // Fetch missing exchange rates from EODHD API
        if (missingAssets.length > 0) {
          await fetchMissingExchangeRates(missingAssets);
        }

        // Fetch all required exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(requiredRates);
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    // Calculate company data
    const companies: CompanyData[] = [];
    let totalPortfolioValue = 0;

    // First pass: calculate market values for allocation calculation
    holdings.forEach((holding, ticker) => {
      const asset = assetData.get(ticker);
      const prices = pricesData.get(ticker);
      const latestPrice = prices?.latest_price?.[0]?.close_price || 0;

      if (asset && latestPrice > 0) {
        const assetCurrency = asset.currency?.code || "USD";
        const marketValue = convertAmount(
          holding.shares * latestPrice,
          assetCurrency,
          displayCurrency,
          exchangeRates
        );
        totalPortfolioValue += marketValue;
      }
    });

    // Second pass: create company data with allocations
    holdings.forEach((holding, ticker) => {
      const asset = assetData.get(ticker);
      const prices = pricesData.get(ticker);
      const dividends = dividendsData.get(asset?.asset_id || 0) || [];

      if (!asset) return;

      const latestPrice = prices?.latest_price?.[0]?.close_price || 0;
      const previousPrice =
        prices?.previous_price?.[0]?.close_price || latestPrice;
      const assetCurrency = asset.currency?.code || "USD";

      // Calculate values
      const avgCost = calculateAverageCost(holding.buyTransactions);
      const costValue = calculateCostValue(holding.buyTransactions);
      const marketValue =
        latestPrice > 0
          ? convertAmount(
              holding.shares * latestPrice,
              assetCurrency,
              displayCurrency,
              exchangeRates
            )
          : 0;

      const dollarReturn =
        marketValue -
        convertAmount(costValue, assetCurrency, displayCurrency, exchangeRates);
      const percentReturn =
        costValue > 0
          ? (dollarReturn /
              convertAmount(
                costValue,
                assetCurrency,
                displayCurrency,
                exchangeRates
              )) *
            100
          : 0;
      const allocation =
        totalPortfolioValue > 0 ? (marketValue / totalPortfolioValue) * 100 : 0;
      const yesterdayChange = latestPrice - previousPrice;
      const dividendIncome = calculateTickerDividendIncome(
        ticker,
        transactions,
        dividends,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );
      const dividendPayments = calculateTickerDividendPayments(
        ticker,
        transactions,
        dividends
      );

      // Calculate DCA values
      const dcaPrice = calculateDCAPrice(holding.buyTransactions);
      const dcaPriceConverted = convertAmount(
        dcaPrice,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );
      const currentPriceConverted = convertAmount(
        latestPrice,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );
      const dcaPercentage = calculateDCAPercentage(
        currentPriceConverted,
        dcaPriceConverted
      );

      // Calculate transaction fees
      const totalTransactionFees = calculateTickerTransactionFees(
        holding.allTransactions,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );

      // Convert 52-week high/low to display currency
      const fiftyTwoWeekLowConverted = asset.latest_metrics?.[0]
        ?.fifty_two_week_low
        ? convertAmount(
            asset.latest_metrics[0].fifty_two_week_low,
            assetCurrency,
            displayCurrency,
            exchangeRates
          )
        : undefined;
      const fiftyTwoWeekHighConverted = asset.latest_metrics?.[0]
        ?.fifty_two_week_high
        ? convertAmount(
            asset.latest_metrics[0].fifty_two_week_high,
            assetCurrency,
            displayCurrency,
            exchangeRates
          )
        : undefined;

      companies.push({
        ticker,
        company: asset.company,
        logo_url: asset.logo_url,
        shares: holding.shares,
        currentPrice: currentPriceConverted,
        avgCost: convertAmount(
          avgCost,
          assetCurrency,
          displayCurrency,
          exchangeRates
        ),
        costValue: convertAmount(
          costValue,
          assetCurrency,
          displayCurrency,
          exchangeRates
        ),
        marketValue,
        dollarReturn,
        percentReturn,
        allocation,
        yesterdayChange: convertAmount(
          yesterdayChange,
          assetCurrency,
          displayCurrency,
          exchangeRates
        ),
        dividendIncome,
        dividendPayments,
        sector: asset.sector?.name,
        industry: asset.industry?.name,
        country: asset.country?.name,
        currency: assetCurrency,
        fiftyTwoWeekLow: asset.latest_metrics?.[0]?.fifty_two_week_low,
        fiftyTwoWeekHigh: asset.latest_metrics?.[0]?.fifty_two_week_high,
        fiftyTwoWeekLowConverted,
        fiftyTwoWeekHighConverted,
        dividendYield: asset.latest_metrics?.[0]?.dividend_yield,
        dcaPrice: dcaPriceConverted,
        dcaPercentage,
        totalTransactionFees,
      });
    });

    // Sort by market value descending
    return companies.sort((a, b) => b.marketValue - a.marketValue);
  } catch (error) {
    console.error("Error fetching companies data:", error);
    throw new Error("Nu s-au putut încărca datele companiilor");
  }
}

/**
 * Fetch portfolio composition data for selected portfolios
 */
export async function getPortfolioCompositionData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioComposition> {
  try {
    if (portfolioIds.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Fetch transactions with asset details
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];

    if (transactions.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Get unique tickers for price lookup
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with references and prices in parallel
    const [assetsResult, pricesResult] = await Promise.all([
      hasuraQuery<{
        ptvuser_asset: Array<{
          asset_id: number;
          ticker: string;
          name: string;
          company: string;
          sector?: { name: string };
          industry?: { name: string };
          currency?: { code: string; name: string; symbol: string };
          country?: { code: string; name: string };
          asset_type?: { name: string };
        }>;
      }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } }),

      hasuraQuery<{
        ptvuser_asset: Array<{
          ticker: string;
          latest_price: Array<{ close_price: number; date: string }>;
        }>;
      }>(GET_LATEST_ASSET_PRICES, {
        variables: {
          tickers: uniqueTickers,
          today: format(new Date(), "yyyy-MM-dd"),
        },
      }),
    ]);

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    const latestPrices = new Map<string, number>();
    pricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();

    if (requiredExchangeRates.length > 0) {
      try {
        // Check which exchange rate assets exist
        const existingAssets = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingAssets = requiredExchangeRates.filter(
          (pair) => !existingAssets.has(pair)
        );

        if (process.env.NODE_ENV === "development") {
          console.log("missing assets:", missingAssets);
          console.log("existing assets:", existingAssets);
          console.log("required rates:", requiredExchangeRates);
        }

        // Fetch missing exchange rates from EODHD API
        if (missingAssets.length > 0) {
          if (process.env.NODE_ENV === "development") {
            console.log(
              `Fetching missing exchange rates: ${missingAssets.join(", ")}`
            );
          }
          await fetchMissingExchangeRates(missingAssets);
        }

        // Fetch all exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
        if (process.env.NODE_ENV === "development") {
          console.log(
            `Loaded ${exchangeRates.size} exchange rates for currency conversion`
          );
          console.log("exchangeRates", exchangeRates);
        }
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioComposition(
      transactions,
      latestPrices,
      assetData,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio composition data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }
}

/**
 * Calculate portfolio holdings at a specific date
 */
export function calculatePortfolioHoldingsAtDate(
  transactions: TransactionWithAssetDetails[],
  targetDate: string
): Map<string, { quantity: number; costBasis: number }> {
  const holdings = new Map<string, { quantity: number; costBasis: number }>();
  const isTargetDate = targetDate === "2024-10-07";

  // Filter transactions up to target date and sort by date
  const relevantTransactions = transactions
    .filter((t) => t.transaction_date <= targetDate)
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  if (isTargetDate && process.env.NODE_ENV === "development") {
    console.log(`\n📋 CALCULATING HOLDINGS FOR ${targetDate}:`);
    console.log(
      `📊 Total transactions up to date: ${relevantTransactions.length}`
    );
    console.log(`📈 Processing transactions in chronological order:`);
  }

  relevantTransactions.forEach((transaction, index) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, costBasis: 0 };

    if (isTargetDate && process.env.NODE_ENV === "development") {
      console.log(`\n  ${index + 1}. Transaction ${transaction.id}:`);
      console.log(`     📅 Date: ${transaction.transaction_date}`);
      console.log(`     🏷️  Ticker: ${ticker}`);
      console.log(`     📊 Type: ${transaction.transaction_type}`);
      console.log(`     💰 Price: ${transaction.price}`);
      console.log(`     📦 Quantity: ${transaction.quantity}`);
      console.log(
        `     📈 Before - Quantity: ${existing.quantity}, Cost Basis: ${existing.costBasis}`
      );
    }

    if (transaction.transaction_type === "BUY") {
      const newQuantity = existing.quantity + transaction.quantity;
      const newCostBasis =
        existing.costBasis + transaction.quantity * transaction.price;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `     ✅ BUY - After: Quantity: ${newQuantity}, Cost Basis: ${newCostBasis}`
        );
      }
    } else if (transaction.transaction_type === "SELL") {
      const newQuantity = existing.quantity - transaction.quantity;
      // Proportionally reduce cost basis
      const costBasisPerShare =
        existing.quantity > 0 ? existing.costBasis / existing.quantity : 0;
      const newCostBasis =
        existing.costBasis - transaction.quantity * costBasisPerShare;
      holdings.set(ticker, {
        quantity: Math.max(0, newQuantity),
        costBasis: Math.max(0, newCostBasis),
      });

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `     🔴 SELL - Cost Basis Per Share: ${costBasisPerShare}`
        );
        console.log(
          `     🔴 SELL - After: Quantity: ${Math.max(
            0,
            newQuantity
          )}, Cost Basis: ${Math.max(0, newCostBasis)}`
        );
      }
    }
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((value, key) => {
    if (value.quantity > 0) {
      filteredHoldings.set(key, value);
    }
  });

  if (isTargetDate && process.env.NODE_ENV === "development") {
    console.log(`\n📊 FINAL HOLDINGS FOR ${targetDate}:`);
    filteredHoldings.forEach((holding, ticker) => {
      console.log(
        `  ${ticker}: ${holding.quantity} shares, Cost Basis: ${holding.costBasis}`
      );
    });
    console.log(`📈 Total unique holdings: ${filteredHoldings.size}\n`);
  }

  return filteredHoldings;
}

/**
 * Calculate portfolio performance over time
 */
export function calculatePortfolioPerformance(
  transactions: TransactionWithAssetDetails[],
  historicalPrices: Map<string, Map<string, number>>, // ticker -> date -> price
  latestPrices: Map<string, number>, // ticker -> latest price (for current value calculation)
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  timePeriod: TimePeriod,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioPerformanceData {
  const { startDate, endDate } = calculateDateRange(timePeriod, transactions);
  if (process.env.NODE_ENV === "development") {
    console.log("startDate", startDate);
    console.log("endDate", endDate);
  }

  // Generate daily dates for all time periods
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    dates.push(current.toISOString().split("T")[0]);
    current.setDate(current.getDate() + 1);
  }

  const performanceData: PortfolioPerformanceDataPoint[] = [];

  // Calculate base portfolio value (cost basis for current holdings) first
  const currentHoldings = calculatePortfolioHoldings(transactions);
  let basePortfolioValue = 0;

  if (process.env.NODE_ENV === "development") {
    console.log(`\n💰 CALCULATING BASE PORTFOLIO VALUE (Cost Basis):`);
    console.log(`📊 Current Holdings:`, currentHoldings);
  }

  currentHoldings.forEach((holding, ticker) => {
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate base portfolio value (cost basis for current holdings)
    let totalCostBasisForTicker = 0;
    let totalBuyQuantity = 0;

    holding.transactions.forEach((t) => {
      if (t.transaction_type === "BUY") {
        totalCostBasisForTicker += t.price * t.quantity;
        totalBuyQuantity += t.quantity;
      }
    });

    const avgCostPerShare =
      totalBuyQuantity > 0 ? totalCostBasisForTicker / totalBuyQuantity : 0;
    const costBasisInOriginalCurrency = holding.quantity * avgCostPerShare;

    if (process.env.NODE_ENV === "development") {
      console.log("costBasisInOriginalCurrency", costBasisInOriginalCurrency);
    }

    const convertedCostBasis = convertAmount(
      costBasisInOriginalCurrency,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );
    basePortfolioValue += convertedCostBasis;

    if (process.env.NODE_ENV === "development") {
      console.log(`  📊 ${ticker}:`);
      console.log(`    💼 Current Quantity: ${holding.quantity}`);
      console.log(
        `    💰 Total Cost Basis: ${totalCostBasisForTicker} ${assetCurrency}`
      );
      console.log(`    📦 Total Buy Quantity: ${totalBuyQuantity}`);
      console.log(
        `    💵 Avg Cost Per Share: ${avgCostPerShare} ${assetCurrency}`
      );
      console.log(
        `    💎 Cost Basis (Original): ${costBasisInOriginalCurrency} ${assetCurrency}`
      );
      console.log(
        `    💎 Cost Basis (Converted): ${convertedCostBasis} ${displayCurrency}`
      );
      console.log(
        `    📈 Running Base Value: ${basePortfolioValue} ${displayCurrency}`
      );
    }
  });

  if (process.env.NODE_ENV === "development") {
    console.log(
      `✅ Total Base Portfolio Value: ${basePortfolioValue} ${displayCurrency}\n`
    );
  }

  // Find the earliest transaction date to determine when user started investing
  const earliestTransactionDate =
    transactions.length > 0
      ? transactions.reduce(
          (earliest, t) =>
            t.transaction_date < earliest ? t.transaction_date : earliest,
          transactions[0].transaction_date
        )
      : null;

  for (const date of dates) {
    const holdings = calculatePortfolioHoldingsAtDate(transactions, date);
    let totalValue = 0;
    let hasValidPrices = false;

    // Check if this date is before the user's first transaction
    const isBeforeFirstTransaction =
      earliestTransactionDate && date < earliestTransactionDate;

    // Detailed logging for specific date
    const isTargetDate = date === "2025-08-07";
    if (isTargetDate && process.env.NODE_ENV === "development") {
      // Enable detailed currency conversion logging
      (global as any).logCurrencyConversion = true;

      console.log(`\n🔍 DETAILED CALCULATION FOR ${date}:`);
      console.log(`📊 Time Period: ${timePeriod}`);
      console.log(`💰 Display Currency: ${displayCurrency}`);
      console.log(`📅 Earliest Transaction Date: ${earliestTransactionDate}`);
      console.log(
        `⏰ Is Before First Transaction: ${isBeforeFirstTransaction}`
      );
      console.log(`🏦 Base Portfolio Value: ${basePortfolioValue}`);
      console.log(`📈 Holdings at this date:`, holdings);
      console.log(
        `💱 Available Exchange Rates:`,
        Array.from(exchangeRates.entries())
      );
    }

    if (isBeforeFirstTransaction) {
      // Use base portfolio value for dates before first transaction
      totalValue = basePortfolioValue;
      hasValidPrices = true;

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`✅ Using base portfolio value: ${totalValue}`);
      }
    } else {
      // Calculate portfolio value using corrected approach:
      // Portfolio Value = Base Portfolio Value + (Market Value of Holdings - Cost Basis of Holdings)
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`🧮 Calculating corrected portfolio value:`);
        console.log(
          `📊 Formula: Base Portfolio Value + (Market Value - Cost Basis of Holdings on Date)`
        );
      }

      let marketValueOfHoldings = 0;
      let costBasisOfHoldings = 0;
      let hasValidData = false;

      holdings.forEach((holding, ticker) => {
        const assetPrices = historicalPrices.get(ticker);
        let priceOnDate = assetPrices?.get(date);
        let priceSource = "exact";

        // If no price for exact date, try to find the closest previous price
        if (!priceOnDate && assetPrices) {
          const availableDates = Array.from(assetPrices.keys()).sort();
          const targetDate = new Date(date);

          for (let i = availableDates.length - 1; i >= 0; i--) {
            const availableDate = new Date(availableDates[i]);
            if (availableDate <= targetDate) {
              priceOnDate = assetPrices.get(availableDates[i]);
              priceSource = `fallback from ${availableDates[i]}`;
              break;
            }
          }
        }

        if (priceOnDate && holding.quantity > 0) {
          const asset = assetData.get(ticker);
          const assetCurrency = asset?.currency?.code || "EUR";

          // Calculate market value in original currency
          const marketValue = holding.quantity * priceOnDate;

          // Calculate cost basis for holdings on this date (average cost per share * quantity)
          const avgCostPerShare = holding.costBasis / holding.quantity;
          const costBasisForHolding = holding.quantity * avgCostPerShare;

          // Convert both to display currency
          const convertedMarketValue = convertAmount(
            marketValue,
            assetCurrency,
            displayCurrency,
            exchangeRates
          );

          const convertedCostBasis = convertAmount(
            costBasisForHolding,
            assetCurrency,
            displayCurrency,
            exchangeRates
          );

          marketValueOfHoldings += convertedMarketValue;
          costBasisOfHoldings += convertedCostBasis;
          hasValidData = true;

          if (isTargetDate && process.env.NODE_ENV === "development") {
            console.log(`  📊 ${ticker}:`);
            console.log(`    💼 Quantity: ${holding.quantity}`);
            console.log(
              `    💵 Price (${priceSource}): ${priceOnDate} ${assetCurrency}`
            );
            console.log(`    💰 Market Value: ${marketValue} ${assetCurrency}`);
            console.log(
              `    💰 Cost Basis: ${costBasisForHolding} ${assetCurrency}`
            );
            console.log(
              `    🔄 Exchange Rate: ${assetCurrency} -> ${displayCurrency}`
            );
            console.log(
              `    💎 Converted Market Value: ${convertedMarketValue} ${displayCurrency}`
            );
            console.log(
              `    � Converted Cost Basis: ${convertedCostBasis} ${displayCurrency}`
            );
            console.log(
              `    �📈 Running Market Value: ${marketValueOfHoldings} ${displayCurrency}`
            );
            console.log(
              `    📈 Running Cost Basis: ${costBasisOfHoldings} ${displayCurrency}`
            );
          }
        } else {
          if (isTargetDate && process.env.NODE_ENV === "development") {
            console.log(`  ❌ ${ticker}: No valid price or zero quantity`);
            console.log(`    💼 Quantity: ${holding.quantity}`);
            console.log(`    💵 Price: ${priceOnDate || "N/A"}`);
          }
        }
      });

      if (hasValidData) {
        // Apply the corrected formula: Base Portfolio Value + (Market Value - Cost Basis of Holdings)
        const gainLoss = marketValueOfHoldings - costBasisOfHoldings;
        totalValue = basePortfolioValue + gainLoss;
        hasValidPrices = true;

        if (isTargetDate && process.env.NODE_ENV === "development") {
          console.log(`\n🧮 CORRECTED CALCULATION BREAKDOWN:`);
          console.log(
            `    🏦 Base Portfolio Value: ${basePortfolioValue} ${displayCurrency}`
          );
          console.log(
            `    📈 Market Value of Holdings: ${marketValueOfHoldings} ${displayCurrency}`
          );
          console.log(
            `    💰 Cost Basis of Holdings: ${costBasisOfHoldings} ${displayCurrency}`
          );
          console.log(`    📊 Gain/Loss: ${gainLoss} ${displayCurrency}`);
          console.log(
            `    ✅ Final Portfolio Value: ${totalValue} ${displayCurrency}`
          );
        }
      }
    }

    // Add data point if we have valid data (either historical prices or base value)
    if (hasValidPrices) {
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `✅ Final Portfolio Value for ${date}: ${totalValue} ${displayCurrency}`
        );
        console.log(`📊 Adding data point to performance data\n`);

        // Disable detailed currency conversion logging
        (global as any).logCurrencyConversion = false;
      }

      performanceData.push({
        date,
        value: totalValue,
        profitLoss: 0, // Will be calculated later based on period start
        profitLossPercentage: 0, // Will be calculated later based on period start
      });
    } else {
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`❌ No valid data for ${date} - skipping data point\n`);

        // Disable detailed currency conversion logging
        (global as any).logCurrencyConversion = false;
      }
    }
  }

  // Calculate current portfolio value using the same logic as composition chart
  // This ensures consistency regardless of time period selection
  let currentValue = 0;

  currentHoldings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate current market value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    currentValue += convertedValue;
  });

  // Calculate time-period-specific profit/loss
  // Get portfolio value at the start of the selected time period
  let portfolioValueAtStart = 0;

  if (performanceData.length > 0) {
    // Use the first data point as the starting value for this time period
    portfolioValueAtStart = performanceData[0].value;
  } else {
    // Fallback to base portfolio value if no historical data
    portfolioValueAtStart = basePortfolioValue;
  }

  // Update all performance data points with correct profit/loss relative to period start
  const updatedPerformanceData = performanceData.map((point) => ({
    ...point,
    profitLoss: point.value - portfolioValueAtStart,
    profitLossPercentage:
      portfolioValueAtStart > 0
        ? ((point.value - portfolioValueAtStart) / portfolioValueAtStart) * 100
        : 0,
  }));

  const totalProfitLoss = currentValue - portfolioValueAtStart;
  const totalProfitLossPercentage =
    portfolioValueAtStart > 0
      ? (totalProfitLoss / portfolioValueAtStart) * 100
      : 0;

  return {
    data: updatedPerformanceData,
    currentValue,
    basePortfolioValue,
    totalProfitLoss,
    totalProfitLossPercentage,
    displayCurrency,
    timePeriod,
  };
}

/**
 * Fetch portfolio performance data for selected portfolios
 */
export async function getPortfolioPerformanceData(
  portfolioIds: string[],
  timePeriod: TimePeriod,
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioPerformanceData> {
  try {
    if (portfolioIds.length === 0) {
      return {
        data: [],
        currentValue: 0,
        basePortfolioValue: 0,
        totalProfitLoss: 0,
        totalProfitLossPercentage: 0,
        displayCurrency,
        timePeriod,
      };
    }

    // Fetch transactions for selected portfolios
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];
    if (transactions.length === 0) {
      return {
        data: [],
        currentValue: 0,
        basePortfolioValue: 0,
        totalProfitLoss: 0,
        totalProfitLossPercentage: 0,
        displayCurrency,
        timePeriod,
      };
    }

    // Get unique tickers
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with references
    const assetsResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
        company: string;
        sector?: { name: string };
        industry?: { name: string };
        currency?: { code: string; name: string; symbol: string };
        country?: { code: string; name: string };
        asset_type?: { name: string };
      }>;
    }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } });

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    // Fetch latest prices for current value calculation (same as composition chart)
    const latestPricesResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        latest_price: Array<{ close_price: number; date: string }>;
      }>;
    }>(GET_LATEST_ASSET_PRICES, {
      variables: {
        tickers: uniqueTickers,
        today: format(new Date(), "yyyy-MM-dd"),
      },
    });

    const latestPrices = new Map<string, number>();
    latestPricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Calculate date range and fetch historical prices (always daily)
    const { startDate, endDate } = calculateDateRange(timePeriod, transactions);

    const historicalPricesResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        historical_prices: Array<{ close_price: number; date: string }>;
      }>;
    }>(GET_HISTORICAL_ASSET_PRICES, {
      variables: { tickers: uniqueTickers, startDate, endDate },
    });

    // Process historical prices into the required format
    const historicalPrices = new Map<string, Map<string, number>>();
    historicalPricesResult.ptvuser_asset?.forEach((asset) => {
      const priceMap = new Map<string, number>();

      // Process daily historical prices
      asset.historical_prices?.forEach(
        (price: { close_price: number; date: string }) => {
          priceMap.set(price.date, price.close_price);
        }
      );

      historicalPrices.set(asset.ticker, priceMap);
    });

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();
    if (requiredExchangeRates.length > 0) {
      try {
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);

        // Check if we have all required exchange rates
        const existingRates = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingRates = requiredExchangeRates.filter(
          (rate) => !existingRates.has(rate)
        );

        if (missingRates.length > 0) {
          await fetchMissingExchangeRates(missingRates);
          exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
        }
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioPerformance(
      transactions,
      historicalPrices,
      latestPrices,
      assetData,
      timePeriod,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio performance data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de performanță ale portofoliului"
    );
  }
}

/**
 * Fetch portfolio metrics data for selected portfolios
 */
export async function getPortfolioMetricsData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioMetrics> {
  try {
    if (portfolioIds.length === 0) {
      return {
        capitalInvested: 0,
        priceGain: 0,
        priceGainPercentage: 0,
        dividends: 0,
        dividendsPercentage: 0,
        realizedGain: 0,
        realizedGainPercentage: 0,
        transactionCosts: 0,
        taxes: 0,
        totalReturn: 0,
        twrr: 0,
        mwrr: 0,
        displayCurrency,
      };
    }

    // Fetch transactions for selected portfolios
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];
    if (transactions.length === 0) {
      return {
        capitalInvested: 0,
        priceGain: 0,
        priceGainPercentage: 0,
        dividends: 0,
        dividendsPercentage: 0,
        realizedGain: 0,
        realizedGainPercentage: 0,
        transactionCosts: 0,
        taxes: 0,
        totalReturn: 0,
        twrr: 0,
        mwrr: 0,
        displayCurrency,
      };
    }

    // Get unique tickers for asset and price lookup
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data, prices, and dividends in parallel
    const [assetsResult, pricesResult] = await Promise.all([
      hasuraQuery<{
        ptvuser_asset: Array<{
          asset_id: number;
          ticker: string;
          name: string;
          company: string;
          sector?: { name: string };
          industry?: { name: string };
          currency?: { code: string; name: string; symbol: string };
          country?: { code: string; name: string };
          asset_type?: { name: string };
        }>;
      }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } }),

      hasuraQuery<{
        ptvuser_asset: Array<{
          ticker: string;
          latest_price: Array<{ close_price: number; date: string }>;
        }>;
      }>(GET_LATEST_ASSET_PRICES, {
        variables: {
          tickers: uniqueTickers,
          today: format(new Date(), "yyyy-MM-dd"),
        },
      }),
    ]);

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    const assetIds: number[] = [];
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
      assetIds.push(asset.asset_id);
    });

    const latestPrices = new Map<string, number>();
    pricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Fetch dividends for the assets
    let dividends: DividendData[] = [];
    if (assetIds.length > 0) {
      try {
        const dividendsResult = await hasuraQuery<{
          ptvuser_dividend: DividendData[];
        }>(GET_PORTFOLIO_DIVIDENDS, {
          variables: { assetIds },
        });
        dividends = dividendsResult.ptvuser_dividend || [];
      } catch (error) {
        console.warn("Failed to fetch dividends:", error);
        // Continue without dividend data
      }
    }

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();
    if (requiredExchangeRates.length > 0) {
      try {
        // Check which exchange rate assets exist
        const existingAssets = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingAssets = requiredExchangeRates.filter(
          (pair) => !existingAssets.has(pair)
        );

        // Fetch missing exchange rates from EODHD API
        if (missingAssets.length > 0) {
          if (process.env.NODE_ENV === "development") {
            console.log(
              `Fetching missing exchange rates: ${missingAssets.join(", ")}`
            );
          }
          await fetchMissingExchangeRates(missingAssets);
        }

        // Fetch all exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioMetrics(
      transactions,
      latestPrices,
      assetData,
      dividends,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio metrics data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de metrici ale portofoliului"
    );
  }
}

/**
 * Fetch dividend card data for selected portfolios
 */
export async function getPortfolioDividendsData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<DividendCardData> {
  try {
    if (portfolioIds.length === 0) {
      return {
        totalDividends: 0,
        dividendYieldTTM: 0,
        yocTTM: 0,
        cagrPayouts: 0,
        yearlyData: [],
        tableData: {},
        displayCurrency,
      };
    }

    // Fetch transactions for selected portfolios
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];
    if (transactions.length === 0) {
      return {
        totalDividends: 0,
        dividendYieldTTM: 0,
        yocTTM: 0,
        cagrPayouts: 0,
        yearlyData: [],
        tableData: {},
        displayCurrency,
      };
    }

    // Get unique tickers and fetch asset details
    const uniqueTickers = [...new Set(transactions.map((t) => t.ticker))];
    const assetsResult = await hasuraQuery<{
      ptvuser_asset: TransactionWithAssetDetails["asset"][];
    }>(GET_ASSETS_WITH_REFERENCES, {
      variables: { tickers: uniqueTickers },
    });

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      if (asset) {
        assetData.set(asset.ticker, asset);
      }
    });

    // Fetch dividends and latest prices for the assets
    const assetIds = Array.from(assetData.values())
      .filter((asset): asset is NonNullable<typeof asset> => asset != null)
      .map((asset) => asset.asset_id);

    let dividends: DividendData[] = [];
    const latestPrices = new Map<string, number>();

    if (assetIds.length > 0) {
      try {
        // Fetch dividends and prices in parallel
        const [dividendsResult, pricesResult] = await Promise.all([
          hasuraQuery<{
            ptvuser_dividend: DividendData[];
          }>(GET_DIVIDENDS_CARD_DATA, {
            variables: { assetIds },
          }),
          hasuraQuery<{
            ptvuser_asset: Array<{
              ticker: string;
              latest_price: Array<{ close_price: number; date: string }>;
            }>;
          }>(GET_LATEST_ASSET_PRICES, {
            variables: {
              tickers: uniqueTickers,
              today: new Date().toISOString().split("T")[0],
            },
          }),
        ]);

        dividends = dividendsResult.ptvuser_dividend || [];

        // Process latest prices
        pricesResult.ptvuser_asset?.forEach((asset) => {
          if (asset.latest_price?.[0]?.close_price) {
            latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
          }
        });
      } catch (error) {
        console.warn("Failed to fetch dividends or prices:", error);
        // Continue without dividend/price data
      }
    }

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);

    // Get exchange rates for display currency
    const displayCurrencyRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    // Always get exchange rates to EUR for currency-independent percentage calculations
    const eurRates = getRequiredExchangeRates(portfolioCurrencies, "EUR");

    // Combine both sets of required rates
    const allRequiredRates = Array.from(
      new Set([...displayCurrencyRates, ...eurRates])
    );

    let exchangeRates: ExchangeRateMap = new Map();
    if (allRequiredRates.length > 0) {
      try {
        // Check if exchange rate assets exist in database
        await checkExchangeRateAssetsExist(allRequiredRates);

        // Fetch exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(allRequiredRates);

        // Fetch missing rates from external API if needed
        const missingRates = allRequiredRates.filter(
          (pair) => !exchangeRates.has(pair)
        );
        if (missingRates.length > 0) {
          await fetchMissingExchangeRates(missingRates);
          // Re-fetch exchange rates after external API call
          exchangeRates = await fetchExchangeRatesFromDB(allRequiredRates);
        }
      } catch (error) {
        console.warn("Failed to fetch exchange rates:", error);
        // Continue with empty exchange rates (will use 1:1 conversion)
      }
    }

    // Calculate dividend card data
    const dividendCardData = calculateDividendCardData(
      transactions,
      dividends,
      assetData,
      displayCurrency,
      exchangeRates,
      latestPrices
    );

    return dividendCardData;
  } catch (error) {
    console.error("Error fetching portfolio dividends data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de dividende ale portofoliului"
    );
  }
}
