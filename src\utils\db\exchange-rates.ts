import { hasuraQuery } from "./hasura";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import {
  createExternalAPIClient,
  ExternalAPIError,
  AssetWithMetricsResponse,
} from "@/lib/external-api-client";

// Cache entry interface for exchange rate requests
interface CacheEntry {
  promise: Promise<void>;
  timestamp: number;
  isResolved: boolean;
}

/**
 * Centralized Exchange Rate Manager
 * Prevents duplicate API calls and implements request deduplication
 */
class ExchangeRateManager {
  private static instance: ExchangeRateManager;
  private requestCache = new Map<string, CacheEntry>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CONCURRENT_REQUESTS = 3; // Limit concurrent API calls
  private activeRequests = 0;
  private requestQueue: Array<() => Promise<void>> = [];

  private constructor() {}

  static getInstance(): ExchangeRateManager {
    if (!ExchangeRateManager.instance) {
      ExchangeRateManager.instance = new ExchangeRateManager();
    }
    return ExchangeRateManager.instance;
  }

  /**
   * Clean expired cache entries
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.requestCache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL && entry.isResolved) {
        this.requestCache.delete(key);
      }
    }
  }

  /**
   * Process queued requests with concurrency control
   */
  private async processQueue(): Promise<void> {
    while (
      this.requestQueue.length > 0 &&
      this.activeRequests < this.MAX_CONCURRENT_REQUESTS
    ) {
      const request = this.requestQueue.shift();
      if (request) {
        this.activeRequests++;
        try {
          await request();
        } finally {
          this.activeRequests--;
          // Process next request in queue
          this.processQueue();
        }
      }
    }
  }

  /**
   * Fetch a single exchange rate with deduplication
   */
  private async fetchSingleExchangeRate(ticker: string): Promise<void> {
    const cacheKey = `exchange_rate_${ticker}`;

    // Clean expired cache entries
    this.cleanExpiredCache();

    // Check if request is already in progress or recently completed
    const existingEntry = this.requestCache.get(cacheKey);
    if (existingEntry) {
      const age = Date.now() - existingEntry.timestamp;
      if (age < this.CACHE_TTL) {
        // Return existing promise (either in progress or recently completed)
        return existingEntry.promise;
      } else {
        // Remove expired entry
        this.requestCache.delete(cacheKey);
      }
    }

    // Create new request promise
    const requestPromise = this.executeExchangeRateRequest(ticker);

    // Cache the promise immediately to prevent duplicate requests
    const cacheEntry: CacheEntry = {
      promise: requestPromise,
      timestamp: Date.now(),
      isResolved: false,
    };

    this.requestCache.set(cacheKey, cacheEntry);

    // Mark as resolved when promise completes
    requestPromise
      .then(() => {
        cacheEntry.isResolved = true;
      })
      .catch(() => {
        // Remove failed requests from cache so they can be retried
        this.requestCache.delete(cacheKey);
      });

    return requestPromise;
  }

  /**
   * Execute the actual API request for exchange rate
   */
  private async executeExchangeRateRequest(ticker: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const executeRequest = async () => {
        try {
          const apiClient = createExternalAPIClient();

          if (process.env.NODE_ENV === "development") {
            console.log(
              `[ExchangeRateManager] Fetching exchange rate data for ${ticker}...`
            );
          }

          const result: AssetWithMetricsResponse =
            await apiClient.getAssetHistory(ticker, {
              refresh: true,
              useMock: false,
              daysHistory: 2,
              providers: [],
            });

          if (
            result.asset &&
            result.asset.prices &&
            result.asset.prices.length > 0
          ) {
            if (process.env.NODE_ENV === "development") {
              console.log(
                `[ExchangeRateManager] Successfully fetched exchange rate data for ${ticker} - ${result.asset.prices.length} price points`
              );
            }
          } else {
            console.warn(
              `[ExchangeRateManager] No price data returned for exchange rate ${ticker}`
            );
          }

          resolve();
        } catch (error) {
          if (error instanceof ExternalAPIError) {
            if (error.statusCode === 404) {
              console.warn(
                `[ExchangeRateManager] Exchange rate ${ticker} not found in EODHD API`
              );
            } else {
              console.error(
                `[ExchangeRateManager] API error fetching exchange rate for ${ticker}:`,
                error.message
              );
            }
          } else {
            console.error(
              `[ExchangeRateManager] Error fetching exchange rate for ${ticker}:`,
              error
            );
          }
          reject(error);
        }
      };

      // Add to queue if at max concurrent requests
      if (this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
        this.requestQueue.push(executeRequest);
      } else {
        executeRequest();
      }
    });
  }

  /**
   * Fetch multiple exchange rates with deduplication and concurrency control
   */
  async fetchExchangeRates(missingPairs: string[]): Promise<void> {
    if (missingPairs.length === 0) {
      return;
    }

    try {
      console.log(
        `[ExchangeRateManager] Fetching ${missingPairs.length} exchange rates with deduplication...`
      );

      // Track initial cache state for monitoring
      const initialCacheSize = this.requestCache.size;

      // Create promises for all missing pairs (deduplication happens in fetchSingleExchangeRate)
      const promises = missingPairs.map((ticker) =>
        this.fetchSingleExchangeRate(ticker)
      );

      // Start processing the queue
      this.processQueue();

      // Wait for all requests to complete
      const results = await Promise.allSettled(promises);

      // Calculate actual API calls made (new cache entries created)
      const finalCacheSize = this.requestCache.size;
      const actualApiCalls = Math.max(0, finalCacheSize - initialCacheSize);

      // Log summary with deduplication metrics
      const successful = results.filter(
        (result) => result.status === "fulfilled"
      ).length;
      const failed = results.filter(
        (result) => result.status === "rejected"
      ).length;
      const deduplicatedCalls = missingPairs.length - actualApiCalls;

      console.log(
        `[ExchangeRateManager] Exchange rate fetch completed: ${successful} successful, ${failed} failed out of ${missingPairs.length} requested. Deduplication saved ${deduplicatedCalls} API calls.`
      );

      if (failed > 0) {
        console.warn(
          `[ExchangeRateManager] ${failed} exchange rate requests failed`
        );
      }

      // Record metrics for monitoring (import will be added later to avoid circular dependency)
      if (
        typeof window !== "undefined" &&
        (window as any).recordExchangeRateRequest
      ) {
        (window as any).recordExchangeRateRequest(missingPairs, actualApiCalls);
      }
    } catch (error) {
      console.error(
        "[ExchangeRateManager] Error in fetchExchangeRates:",
        error
      );
      throw new Error("Nu s-au putut obține cursurile de schimb lipsă");
    }
  }

  /**
   * Clear all cached requests (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.requestCache.clear();
    console.log("[ExchangeRateManager] Cache cleared");
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    size: number;
    activeRequests: number;
    queueLength: number;
  } {
    return {
      size: this.requestCache.size,
      activeRequests: this.activeRequests,
      queueLength: this.requestQueue.length,
    };
  }
}

// Types for exchange rate data
export interface ExchangeRate {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  date: string;
}

export type ExchangeRateMap = Map<string, number>;

// GraphQL query to get exchange rates from asset_price table
export const GET_EXCHANGE_RATES = `
  query GetExchangeRates($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      latest_price: asset_prices(
        order_by: {date: desc}
        limit: 1
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

// GraphQL query to check if exchange rate assets exist
export const GET_EXCHANGE_RATE_ASSETS = `
  query GetExchangeRateAssets($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
    }
  }
`;

/**
 * Generate currency pair ticker for EODHD format with .FOREX suffix
 * Examples: EURUSD.FOREX, USDEUR.FOREX, EURRON.FOREX, RONEUR.FOREX
 */
export function generateCurrencyPairTicker(
  fromCurrency: string,
  toCurrency: string
): string {
  return `${fromCurrency.toUpperCase()}${toCurrency.toUpperCase()}.FOREX`;
}

/**
 * Get all required exchange rate pairs for portfolio conversion
 */
export function getRequiredExchangeRates(
  portfolioCurrencies: string[],
  targetCurrency: SupportedCurrency
): string[] {
  const uniqueCurrencies = Array.from(new Set(portfolioCurrencies));
  const requiredPairs: string[] = [];

  for (const currency of uniqueCurrencies) {
    if (currency !== targetCurrency) {
      // Direct conversion (e.g., USD to EUR = USDEUR)
      const directPair = generateCurrencyPairTicker(currency, targetCurrency);
      requiredPairs.push(directPair);

      // Also add reverse pair for fallback (e.g., EUR to USD = EURUSD)
      const reversePair = generateCurrencyPairTicker(targetCurrency, currency);
      requiredPairs.push(reversePair);
    }
  }

  return Array.from(new Set(requiredPairs)); // Remove duplicates
}

/**
 * Fetch exchange rates from database
 */
export async function fetchExchangeRatesFromDB(
  currencyPairs: string[]
): Promise<ExchangeRateMap> {
  const exchangeRates = new Map<string, number>();

  if (currencyPairs.length === 0) {
    return exchangeRates;
  }

  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        latest_price: Array<{
          close_price: number;
          date: string;
        }>;
      }>;
    }>(GET_EXCHANGE_RATES, {
      variables: { tickers: currencyPairs },
    });

    result.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        exchangeRates.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    return exchangeRates;
  } catch (error) {
    console.error("Error fetching exchange rates from database:", error);
    return exchangeRates;
  }
}

/**
 * Check which exchange rate assets exist in database
 */
export async function checkExchangeRateAssetsExist(
  currencyPairs: string[]
): Promise<Set<string>> {
  const existingAssets = new Set<string>();

  if (currencyPairs.length === 0) {
    return existingAssets;
  }

  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
      }>;
    }>(GET_EXCHANGE_RATE_ASSETS, {
      variables: { tickers: currencyPairs },
    });

    result.ptvuser_asset?.forEach((asset) => {
      existingAssets.add(asset.ticker);
    });

    return existingAssets;
  } catch (error) {
    console.error("Error checking exchange rate assets:", error);
    return existingAssets;
  }
}

/**
 * Fetch missing exchange rates from EODHD API
 * Uses centralized ExchangeRateManager to prevent duplicate API calls
 */
export async function fetchMissingExchangeRates(
  missingPairs: string[]
): Promise<void> {
  if (missingPairs.length === 0) {
    return;
  }

  try {
    // Use the centralized exchange rate manager for deduplication
    const exchangeRateManager = ExchangeRateManager.getInstance();
    await exchangeRateManager.fetchExchangeRates(missingPairs);
  } catch (error) {
    console.error("Error in fetchMissingExchangeRates:", error);
    throw new Error("Nu s-au putut obține cursurile de schimb lipsă");
  }
}

/**
 * Get the exchange rate manager instance for direct access
 * Useful for monitoring and cache management
 */
export function getExchangeRateManager(): ExchangeRateManager {
  return ExchangeRateManager.getInstance();
}

/**
 * Clear the exchange rate cache
 * Useful for testing or manual refresh scenarios
 */
export function clearExchangeRateCache(): void {
  const manager = ExchangeRateManager.getInstance();
  manager.clearCache();
}

/**
 * Get exchange rate manager statistics
 * Useful for monitoring and debugging
 */
export function getExchangeRateStats(): {
  size: number;
  activeRequests: number;
  queueLength: number;
} {
  const manager = ExchangeRateManager.getInstance();
  return manager.getCacheStats();
}

/**
 * Convert amount from one currency to another
 */
export function convertAmount(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRates: ExchangeRateMap
): number {
  // If same currency, no conversion needed
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Try direct conversion (e.g., USD to EUR using USDEUR rate)
  const directPair = generateCurrencyPairTicker(fromCurrency, toCurrency);
  const directRate = exchangeRates.get(directPair);

  if (directRate && directRate > 0) {
    const convertedAmount = amount * directRate;

    return convertedAmount;
  }

  // Try inverse conversion (e.g., USD to EUR using EURUSD rate = 1/rate)
  const inversePair = generateCurrencyPairTicker(toCurrency, fromCurrency);
  const inverseRate = exchangeRates.get(inversePair);

  console.log("paris", directPair, inversePair);

  if (inverseRate && inverseRate > 0) {
    const convertedAmount = amount / inverseRate;

    return convertedAmount;
  }

  // If no exchange rate available, return original amount

  console.warn(`No exchange rate found for ${fromCurrency} to ${toCurrency}`);
  return amount;
}

/**
 * Get all unique currencies from portfolio transactions
 */
export function extractPortfolioCurrencies(
  assetData: Map<string, any>
): string[] {
  const currencies = new Set<string>();

  assetData.forEach((asset) => {
    if (asset?.currency?.code) {
      currencies.add(asset.currency.code);
    } else if (asset?.currency?.name) {
      currencies.add(asset.currency.name);
    }
  });

  return Array.from(currencies);
}
