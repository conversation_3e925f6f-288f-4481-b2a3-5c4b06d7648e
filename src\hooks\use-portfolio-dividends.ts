"use client";

import { useQuery } from "@tanstack/react-query";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { DividendCardData } from "@/utils/db/dashboard-queries";

// Query keys for portfolio dividends
export const portfolioDividendsKeys = {
  all: ["portfolio-dividends"] as const,
  lists: () => [...portfolioDividendsKeys.all, "list"] as const,
  list: (portfolioIds: string[], displayCurrency: SupportedCurrency) =>
    [
      ...portfolioDividendsKeys.lists(),
      { portfolioIds, displayCurrency },
    ] as const,
};

// API response interface
interface PortfolioDividendsResponse {
  success: boolean;
  data: DividendCardData;
  message: string;
}

// Fetch function for portfolio dividends data
async function fetchPortfolioDividends(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<DividendCardData> {
  if (portfolioIds.length === 0) {
    return {
      totalDividends: 0,
      dividendYieldTTM: 0,
      yocTTM: 0,
      cagrPayouts: 0,
      yearlyData: [],
      tableData: {},
      displayCurrency,
    };
  }

  // Use GET for smaller lists, POST for larger ones - more than 4 portfolios
  const usePost = portfolioIds.length > 4;

  let response: Response;

  if (usePost) {
    response = await fetch("/api/dashboard/portfolio-dividends", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ portfolioIds, displayCurrency }),
    });
  } else {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
      displayCurrency,
    });
    response = await fetch(`/api/dashboard/portfolio-dividends?${params}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        "Nu s-au putut încărca datele de dividende ale portofoliului"
    );
  }

  const result: PortfolioDividendsResponse = await response.json();
  return result.data;
}

// Hook for fetching portfolio dividends
export function usePortfolioDividends(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
) {
  return useQuery<DividendCardData>({
    queryKey: portfolioDividendsKeys.list(portfolioIds, displayCurrency),
    queryFn: () => fetchPortfolioDividends(portfolioIds, displayCurrency),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
